#!/usr/bin/env python3
"""
DRC热点检测评估测试脚本

该脚本专门用于测试和验证DRC热点检测评估器的功能，
提供详细的评估报告和性能分析。

使用方法:
python tools/test_drc_evaluation.py --config configs/rtdetr/rtdetr_r18vd_6x_coco.yml --checkpoint path/to/checkpoint.pth

作者: DRC评估系统
日期: 2024
"""

import os
import sys
import argparse
import json
import torch
import numpy as np
from pathlib import Path
from pycocotools.coco import COCO

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from src.core import YAMLConfig
from src.data import get_coco_api_from_dataset
from src.data.coco.coco_eval import DRCHotspotEvaluator
from src.solver.det_engine import evaluate
from src.misc import dist


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='DRC热点检测评估测试')
    
    parser.add_argument('--config', required=True, 
                       help='配置文件路径')
    parser.add_argument('--checkpoint', required=True,
                       help='模型检查点路径')
    parser.add_argument('--output_dir', default='./drc_evaluation_output',
                       help='输出目录')
    parser.add_argument('--iou_threshold', type=float, default=0.6,
                       help='IoU阈值')
    parser.add_argument('--multi_iou', action='store_true',
                       help='是否进行多IoU阈值分析')
    parser.add_argument('--confidence_analysis', action='store_true',
                       help='是否进行置信度分析')
    parser.add_argument('--save_error_cases', action='store_true',
                       help='是否保存错误案例')
    parser.add_argument('--device', default='cuda',
                       help='设备类型')
    
    return parser.parse_args()


def load_model_and_config(config_path, checkpoint_path, device):
    """加载模型和配置"""
    print(f"加载配置文件: {config_path}")
    cfg = YAMLConfig(config_path)
    
    print(f"加载模型检查点: {checkpoint_path}")
    model = cfg.model
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    return model, cfg


def run_single_iou_evaluation(model, cfg, device, output_dir, iou_threshold=0.6):
    """运行单个IoU阈值的评估"""
    print(f"\n{'='*60}")
    print(f"运行DRC评估 - IoU阈值: {iou_threshold}")
    print(f"{'='*60}")
    
    # 获取数据加载器和数据集
    val_dataloader = cfg.val_dataloader
    base_ds = get_coco_api_from_dataset(val_dataloader.dataset)
    
    # 配置DRC评估器
    drc_config = {
        'iou_threshold': iou_threshold,
        'hotspot_class_id': 1,
        'save_results': True
    }
    
    # 运行评估
    test_stats, coco_evaluator = evaluate(
        model, cfg.criterion, cfg.postprocessor, 
        val_dataloader, base_ds, device, output_dir, drc_config
    )
    
    return test_stats


def run_multi_iou_analysis(model, cfg, device, output_dir, iou_thresholds):
    """运行多IoU阈值分析"""
    print(f"\n{'='*60}")
    print("多IoU阈值分析")
    print(f"{'='*60}")
    
    results = {}
    
    for iou_threshold in iou_thresholds:
        print(f"\n分析IoU阈值: {iou_threshold}")
        test_stats = run_single_iou_evaluation(model, cfg, device, output_dir, iou_threshold)
        
        # 提取DRC指标
        drc_metrics = {}
        for key, value in test_stats.items():
            if key.startswith('drc_'):
                metric_name = key.replace('drc_', '')
                drc_metrics[metric_name] = value[0] if isinstance(value, list) else value
        
        results[iou_threshold] = drc_metrics
    
    # 保存多IoU分析结果
    multi_iou_path = Path(output_dir) / 'multi_iou_analysis.json'
    with open(multi_iou_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 输出对比分析
    print(f"\n{'='*80}")
    print("多IoU阈值对比分析")
    print(f"{'='*80}")
    print(f"{'IoU阈值':<10} {'Accuracy':<10} {'Precision':<10} {'F1 Score':<10} {'False Alarm':<12}")
    print("-" * 80)
    
    for iou_threshold, metrics in results.items():
        print(f"{iou_threshold:<10.2f} {metrics.get('accuracy', 0):<10.4f} "
              f"{metrics.get('precision', 0):<10.4f} {metrics.get('f1_score', 0):<10.4f} "
              f"{metrics.get('false_alarm', 0):<12}")
    
    return results


def generate_performance_report(test_stats, output_dir):
    """生成性能分析报告"""
    print(f"\n{'='*60}")
    print("生成性能分析报告")
    print(f"{'='*60}")
    
    # 提取DRC指标
    drc_metrics = {}
    coco_metrics = {}
    
    for key, value in test_stats.items():
        if key.startswith('drc_'):
            metric_name = key.replace('drc_', '')
            drc_metrics[metric_name] = value[0] if isinstance(value, list) else value
        elif key.startswith('coco_'):
            coco_metrics[key] = value
    
    # 生成报告
    report = {
        'evaluation_summary': {
            'drc_metrics': drc_metrics,
            'coco_metrics': coco_metrics
        },
        'performance_analysis': {
            'strengths': [],
            'weaknesses': [],
            'recommendations': []
        }
    }
    
    # 性能分析
    accuracy = drc_metrics.get('accuracy', 0)
    precision = drc_metrics.get('precision', 0)
    f1_score = drc_metrics.get('f1_score', 0)
    false_alarm = drc_metrics.get('false_alarm', 0)
    
    # 分析优势
    if accuracy > 0.8:
        report['performance_analysis']['strengths'].append("高召回率：模型能够检测到大部分热点")
    if precision > 0.8:
        report['performance_analysis']['strengths'].append("高精确度：模型的误报率较低")
    if f1_score > 0.8:
        report['performance_analysis']['strengths'].append("优秀的综合性能：F1分数表现良好")
    
    # 分析弱点
    if accuracy < 0.6:
        report['performance_analysis']['weaknesses'].append("召回率偏低：可能遗漏较多热点")
    if precision < 0.6:
        report['performance_analysis']['weaknesses'].append("精确度偏低：存在较多误报")
    if false_alarm > 100:
        report['performance_analysis']['weaknesses'].append("误报数量过多：需要优化模型以减少False Positive")
    
    # 优化建议
    if accuracy < precision:
        report['performance_analysis']['recommendations'].append("考虑降低检测阈值以提高召回率")
    if precision < accuracy:
        report['performance_analysis']['recommendations'].append("考虑提高检测阈值以减少误报")
    if f1_score < 0.7:
        report['performance_analysis']['recommendations'].append("需要进一步优化模型架构或训练策略")
    
    # 保存报告
    report_path = Path(output_dir) / 'performance_report.json'
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"性能分析报告已保存到: {report_path}")
    return report


def main():
    """主函数"""
    args = parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("DRC热点检测评估测试")
    print("=" * 60)
    print(f"配置文件: {args.config}")
    print(f"检查点: {args.checkpoint}")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {args.device}")
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型和配置
    model, cfg = load_model_and_config(args.config, args.checkpoint, device)
    
    # 运行评估
    if args.multi_iou:
        # 多IoU阈值分析
        iou_thresholds = [0.5, 0.6, 0.7, 0.8]
        multi_iou_results = run_multi_iou_analysis(model, cfg, device, str(output_dir), iou_thresholds)
    else:
        # 单IoU阈值评估
        test_stats = run_single_iou_evaluation(model, cfg, device, str(output_dir), args.iou_threshold)
        
        # 生成性能报告
        performance_report = generate_performance_report(test_stats, str(output_dir))
    
    print(f"\n{'='*60}")
    print("评估完成！")
    print(f"结果已保存到: {args.output_dir}")
    print(f"{'='*60}")


if __name__ == '__main__':
    main()
