#!/usr/bin/env python3
"""
DRC热点检测消融实验自动化脚本

该脚本用于系统性地评估不同损失函数组件的贡献，
自动化执行多个消融实验并生成对比分析结果。

使用方法:
python tools/ablation_study.py --base_config configs/rtdetr/rtdetr_r18vd_6x_coco.yml --epochs 20

作者: DRC消融实验系统
日期: 2024
"""

import os
import sys
import json
import yaml
import shutil
import argparse
import subprocess
import time
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from src.core import YAMLConfig


class AblationExperimentManager:
    """消融实验管理器"""
    
    def __init__(self, base_config_path: str, epochs: int = 20, gpu_ids: str = "0"):
        """
        初始化消融实验管理器
        
        Args:
            base_config_path: 基础配置文件路径
            epochs: 训练轮数
            gpu_ids: GPU设备ID
        """
        self.base_config_path = base_config_path
        self.epochs = epochs
        self.gpu_ids = gpu_ids
        self.base_output_dir = Path("./output/ablation")
        self.base_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 实验配置
        self.experiments = self.define_experiments()
        
        # 结果存储
        self.results = {}
        
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.base_output_dir / "ablation_study.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def define_experiments(self) -> List[Dict]:
        """定义消融实验配置"""
        experiments = [
            {
                "name": "rm-VFL",
                "description": "移除Varifocal Loss",
                "config_modifications": {
                    "losses": ["boxes", "fppl"],
                    "weight_dict": {
                        "loss_bbox": 5,
                        "loss_giou": 2,
                        "loss_fppl": 1.5
                    }
                },
                "expected_impact": "分类性能下降，可能影响整体检测精度"
            },
            {
                "name": "rm-FPPL",
                "description": "移除False Positive Penalty Loss",
                "config_modifications": {
                    "losses": ["vfl", "boxes"],
                    "weight_dict": {
                        "loss_vfl": 1,
                        "loss_bbox": 5,
                        "loss_giou": 2
                    }
                },
                "expected_impact": "False Positive增加，精确度下降"
            },
            {
                "name": "replace-IoU",
                "description": "替换边界框损失为简单IoU损失",
                "config_modifications": {
                    "losses": ["vfl", "iou", "fppl"],
                    "weight_dict": {
                        "loss_vfl": 1,
                        "loss_iou": 7,  # 替代bbox+giou的总权重
                        "loss_fppl": 1.5
                    }
                },
                "expected_impact": "定位精度可能下降，几何一致性变差",
                "requires_new_loss": True
            }
        ]
        return experiments
    
    def create_experiment_config(self, experiment: Dict) -> str:
        """为实验创建临时配置文件"""
        # 加载基础配置
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 修改配置
        modifications = experiment["config_modifications"]
        
        # 修改损失函数配置
        if "losses" in modifications:
            config["SetCriterion"]["losses"] = modifications["losses"]
        
        if "weight_dict" in modifications:
            config["SetCriterion"]["weight_dict"] = modifications["weight_dict"]
        
        # 修改训练轮数
        config["epoches"] = self.epochs
        
        # 修改输出目录
        exp_output_dir = self.base_output_dir / experiment["name"]
        config["output_dir"] = str(exp_output_dir)
        
        # 保存临时配置文件
        temp_config_path = self.base_output_dir / f"{experiment['name']}_config.yml"
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return str(temp_config_path)
    
    def implement_iou_loss(self):
        """实现简单IoU损失函数"""
        iou_loss_code = '''
    def loss_iou(self, outputs, targets, indices, num_boxes):
        """Simple IoU loss for ablation study"""
        assert 'pred_boxes' in outputs
        idx = self._get_src_permutation_idx(indices)
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([t['boxes'][i] for t, (_, i) in zip(targets, indices)], dim=0)
        
        # Calculate IoU
        ious, _ = box_iou(box_cxcywh_to_xyxy(src_boxes), box_cxcywh_to_xyxy(target_boxes))
        ious = torch.diag(ious)
        
        # IoU loss = 1 - IoU
        loss_iou = 1 - ious
        losses = {'loss_iou': loss_iou.sum() / num_boxes}
        return losses
        '''
        
        # 检查是否已经添加了IoU损失
        criterion_file = "src/zoo/rtdetr/rtdetr_criterion.py"
        with open(criterion_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "def loss_iou" not in content:
            # 在loss_fppl方法后添加IoU损失
            insert_pos = content.find("    def get_loss(self, loss, outputs, targets, indices, num_boxes, **kwargs):")
            if insert_pos != -1:
                new_content = content[:insert_pos] + iou_loss_code + "\n" + content[insert_pos:]
                
                # 同时添加到loss_map中
                loss_map_pos = new_content.find("'fppl': self.loss_fppl,  # 🔥 添加FPPL损失")
                if loss_map_pos != -1:
                    insert_pos = new_content.find("}", loss_map_pos)
                    new_content = (new_content[:insert_pos] + 
                                 "            'iou': self.loss_iou,  # 🔥 添加简单IoU损失\n        " + 
                                 new_content[insert_pos:])
                
                # 备份原文件
                shutil.copy2(criterion_file, criterion_file + ".backup")
                
                # 写入修改后的内容
                with open(criterion_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.logger.info("已添加简单IoU损失函数")
    
    def restore_criterion_file(self):
        """恢复原始的criterion文件"""
        criterion_file = "src/zoo/rtdetr/rtdetr_criterion.py"
        backup_file = criterion_file + ".backup"
        
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, criterion_file)
            os.remove(backup_file)
            self.logger.info("已恢复原始criterion文件")
    
    def run_experiment(self, experiment: Dict) -> Dict:
        """运行单个消融实验"""
        exp_name = experiment["name"]
        self.logger.info(f"开始实验: {exp_name} - {experiment['description']}")
        
        # 创建实验目录
        exp_dir = self.base_output_dir / exp_name
        exp_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查是否需要实现新的损失函数
        if experiment.get("requires_new_loss", False):
            self.implement_iou_loss()
        
        try:
            # 创建实验配置
            config_path = self.create_experiment_config(experiment)
            
            # 复制配置到实验目录
            shutil.copy2(config_path, exp_dir / "config.yml")
            
            # 构建训练命令
            train_cmd = [
                "python", "tools/train.py",
                "--config", config_path,
                "--device", f"cuda:{self.gpu_ids}"
            ]
            
            # 设置环境变量
            env = os.environ.copy()
            env["CUDA_VISIBLE_DEVICES"] = self.gpu_ids
            
            # 运行训练
            self.logger.info(f"执行命令: {' '.join(train_cmd)}")
            start_time = time.time()
            
            process = subprocess.Popen(
                train_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                env=env,
                cwd=os.getcwd()
            )
            
            # 实时输出日志
            log_file = exp_dir / "training.log"
            with open(log_file, 'w', encoding='utf-8') as f:
                for line in process.stdout:
                    print(f"[{exp_name}] {line.strip()}")
                    f.write(line)
                    f.flush()
            
            process.wait()
            end_time = time.time()
            
            if process.returncode == 0:
                self.logger.info(f"实验 {exp_name} 完成，耗时: {end_time - start_time:.2f}秒")
                
                # 提取实验结果
                results = self.extract_experiment_results(exp_dir)
                results["status"] = "success"
                results["duration"] = end_time - start_time
                
            else:
                self.logger.error(f"实验 {exp_name} 失败，返回码: {process.returncode}")
                results = {"status": "failed", "error_code": process.returncode}
            
            # 清理临时配置文件
            if os.path.exists(config_path):
                os.remove(config_path)
                
            return results
            
        except Exception as e:
            self.logger.error(f"实验 {exp_name} 发生异常: {str(e)}")
            return {"status": "error", "error": str(e)}
        
        finally:
            # 恢复原始文件
            if experiment.get("requires_new_loss", False):
                self.restore_criterion_file()
    
    def extract_experiment_results(self, exp_dir: Path) -> Dict:
        """从实验目录提取结果"""
        results = {}
        
        # 读取日志文件
        log_file = exp_dir / "log.txt"
        if log_file.exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 提取最后一行的结果（JSON格式）
                for line in reversed(lines):
                    line = line.strip()
                    if line.startswith('{') and line.endswith('}'):
                        try:
                            last_epoch_data = json.loads(line)
                            
                            # 提取关键指标
                            results.update({
                                "final_epoch": last_epoch_data.get("epoch", -1),
                                "coco_map": last_epoch_data.get("test_coco_eval_bbox", [0])[0],
                                "drc_accuracy": last_epoch_data.get("test_drc_accuracy", [0])[0],
                                "drc_precision": last_epoch_data.get("test_drc_precision", [0])[0],
                                "drc_recall": last_epoch_data.get("test_drc_recall", [0])[0],
                                "drc_f1_score": last_epoch_data.get("test_drc_f1_score", [0])[0],
                                "drc_false_alarm": last_epoch_data.get("test_drc_false_alarm", [0])[0],
                            })
                            break
                        except json.JSONDecodeError:
                            continue
                            
            except Exception as e:
                self.logger.warning(f"无法解析日志文件 {log_file}: {e}")
        
        return results

    def run_all_experiments(self):
        """运行所有消融实验"""
        self.logger.info("开始消融实验研究")
        self.logger.info(f"总共 {len(self.experiments)} 个实验")

        # 记录开始时间
        study_start_time = time.time()

        for i, experiment in enumerate(self.experiments, 1):
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"实验 {i}/{len(self.experiments)}: {experiment['name']}")
            self.logger.info(f"描述: {experiment['description']}")
            self.logger.info(f"预期影响: {experiment['expected_impact']}")
            self.logger.info(f"{'='*60}")

            # 检查是否已完成
            exp_dir = self.base_output_dir / experiment["name"]
            if self.is_experiment_completed(exp_dir):
                self.logger.info(f"实验 {experiment['name']} 已完成，跳过")
                results = self.extract_experiment_results(exp_dir)
                results["status"] = "completed"
            else:
                # 运行实验
                results = self.run_experiment(experiment)

            # 保存结果
            self.results[experiment["name"]] = {
                "experiment": experiment,
                "results": results
            }

            # 保存中间结果
            self.save_intermediate_results()

            self.logger.info(f"实验 {experiment['name']} 状态: {results.get('status', 'unknown')}")

        # 计算总耗时
        study_end_time = time.time()
        total_duration = study_end_time - study_start_time

        self.logger.info(f"\n{'='*60}")
        self.logger.info("所有消融实验完成！")
        self.logger.info(f"总耗时: {total_duration:.2f}秒 ({total_duration/3600:.2f}小时)")
        self.logger.info(f"{'='*60}")

        # 生成最终报告
        self.generate_final_report()

    def is_experiment_completed(self, exp_dir: Path) -> bool:
        """检查实验是否已完成"""
        if not exp_dir.exists():
            return False

        # 检查是否有最终的检查点文件
        checkpoint_dir = exp_dir / "checkpoints"
        if checkpoint_dir.exists():
            checkpoints = list(checkpoint_dir.glob("*.pth"))
            if len(checkpoints) >= self.epochs:
                return True

        # 检查日志文件是否包含完整的训练记录
        log_file = exp_dir / "log.txt"
        if log_file.exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 检查是否包含最后一个epoch的记录
                    if f'"epoch": {self.epochs - 1}' in content:
                        return True
            except:
                pass

        return False

    def save_intermediate_results(self):
        """保存中间结果"""
        results_file = self.base_output_dir / "intermediate_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

    def generate_final_report(self):
        """生成最终的消融实验报告"""
        self.logger.info("生成最终消融实验报告...")

        # 保存完整结果
        final_results_file = self.base_output_dir / "ablation_results.json"
        with open(final_results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        # 生成对比分析
        self.generate_comparison_analysis()

        # 生成可视化图表
        self.generate_visualization()

        # 生成Markdown报告
        self.generate_markdown_report()

        self.logger.info(f"最终报告已保存到: {self.base_output_dir}")

    def generate_comparison_analysis(self):
        """生成对比分析"""
        analysis = {
            "summary": {
                "total_experiments": len(self.experiments),
                "successful_experiments": 0,
                "failed_experiments": 0
            },
            "metrics_comparison": {},
            "insights": []
        }

        # 收集所有成功实验的指标
        successful_results = {}
        for exp_name, exp_data in self.results.items():
            if exp_data["results"].get("status") in ["success", "completed"]:
                analysis["summary"]["successful_experiments"] += 1
                successful_results[exp_name] = exp_data["results"]
            else:
                analysis["summary"]["failed_experiments"] += 1

        # 对比分析
        if successful_results:
            metrics = ["coco_map", "drc_accuracy", "drc_precision", "drc_recall", "drc_f1_score", "drc_false_alarm"]

            for metric in metrics:
                values = {}
                for exp_name, results in successful_results.items():
                    if metric in results:
                        values[exp_name] = results[metric]

                if values:
                    analysis["metrics_comparison"][metric] = {
                        "values": values,
                        "best": max(values.items(), key=lambda x: x[1] if metric != "drc_false_alarm" else -x[1]),
                        "worst": min(values.items(), key=lambda x: x[1] if metric != "drc_false_alarm" else -x[1])
                    }

        # 生成洞察
        self.generate_insights(analysis, successful_results)

        # 保存分析结果
        analysis_file = self.base_output_dir / "comparison_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)

    def generate_insights(self, analysis: Dict, successful_results: Dict):
        """生成实验洞察"""
        insights = []

        # 分析各组件的重要性
        if "rm-VFL" in successful_results and "rm-FPPL" in successful_results:
            vfl_impact = successful_results["rm-VFL"].get("coco_map", 0)
            fppl_impact = successful_results["rm-FPPL"].get("drc_f1_score", 0)

            if vfl_impact < 0.9:  # 假设基线mAP > 0.9
                insights.append("移除Varifocal Loss显著影响检测精度，说明VFL对整体性能至关重要")

            if fppl_impact < 0.95:  # 假设基线F1 > 0.95
                insights.append("移除FPPL导致F1分数下降，证明FPPL有效降低了False Positive")

        # 分析损失函数替换的影响
        if "replace-IoU" in successful_results:
            iou_precision = successful_results["replace-IoU"].get("drc_precision", 0)
            if iou_precision < 0.95:
                insights.append("简单IoU损失的定位精度不如L1+GIoU组合，说明几何一致性约束的重要性")

        analysis["insights"] = insights

    def generate_visualization(self):
        """生成可视化图表"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # 收集成功实验的数据
            successful_results = {}
            for exp_name, exp_data in self.results.items():
                if exp_data["results"].get("status") in ["success", "completed"]:
                    successful_results[exp_name] = exp_data["results"]

            if not successful_results:
                self.logger.warning("没有成功的实验结果，跳过可视化生成")
                return

            # 创建对比图表
            metrics = ["coco_map", "drc_accuracy", "drc_precision", "drc_recall", "drc_f1_score"]
            metric_names = ["COCO mAP", "DRC Accuracy", "DRC Precision", "DRC Recall", "DRC F1 Score"]

            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            axes = axes.flatten()

            for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
                if i >= len(axes):
                    break

                exp_names = []
                values = []

                for exp_name, results in successful_results.items():
                    if metric in results:
                        exp_names.append(exp_name)
                        values.append(results[metric])

                if values:
                    bars = axes[i].bar(exp_names, values)
                    axes[i].set_title(metric_name)
                    axes[i].set_ylabel("Score")
                    axes[i].tick_params(axis='x', rotation=45)

                    # 添加数值标签
                    for bar, value in zip(bars, values):
                        axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                                   f'{value:.3f}', ha='center', va='bottom')

            # False Alarm对比（越小越好）
            if len(axes) > 5:
                exp_names = []
                values = []

                for exp_name, results in successful_results.items():
                    if "drc_false_alarm" in results:
                        exp_names.append(exp_name)
                        values.append(results["drc_false_alarm"])

                if values:
                    bars = axes[5].bar(exp_names, values, color='red', alpha=0.7)
                    axes[5].set_title("DRC False Alarm (Lower is Better)")
                    axes[5].set_ylabel("Count")
                    axes[5].tick_params(axis='x', rotation=45)

                    # 添加数值标签
                    for bar, value in zip(bars, values):
                        axes[5].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                                   f'{int(value)}', ha='center', va='bottom')

            plt.tight_layout()
            plt.savefig(self.base_output_dir / "ablation_comparison.png", dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info("可视化图表已生成: ablation_comparison.png")

        except ImportError:
            self.logger.warning("matplotlib未安装，跳过可视化生成")
        except Exception as e:
            self.logger.error(f"生成可视化图表时发生错误: {e}")

    def generate_markdown_report(self):
        """生成Markdown格式的报告"""
        report_content = []

        # 标题
        report_content.append("# DRC热点检测消融实验报告")
        report_content.append(f"\n**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_content.append(f"**训练轮数**: {self.epochs}")
        report_content.append(f"**GPU设备**: {self.gpu_ids}")

        # 实验概述
        report_content.append("\n## 实验概述")
        successful_count = sum(1 for exp_data in self.results.values()
                             if exp_data["results"].get("status") in ["success", "completed"])
        failed_count = len(self.results) - successful_count

        report_content.append(f"- 总实验数: {len(self.results)}")
        report_content.append(f"- 成功实验: {successful_count}")
        report_content.append(f"- 失败实验: {failed_count}")

        # 实验详情
        report_content.append("\n## 实验详情")

        for exp_name, exp_data in self.results.items():
            experiment = exp_data["experiment"]
            results = exp_data["results"]

            report_content.append(f"\n### {exp_name}: {experiment['description']}")
            report_content.append(f"**预期影响**: {experiment['expected_impact']}")
            report_content.append(f"**状态**: {results.get('status', 'unknown')}")

            if results.get("status") in ["success", "completed"]:
                report_content.append("\n**结果指标**:")
                metrics = [
                    ("COCO mAP", "coco_map", ".4f"),
                    ("DRC Accuracy", "drc_accuracy", ".4f"),
                    ("DRC Precision", "drc_precision", ".4f"),
                    ("DRC Recall", "drc_recall", ".4f"),
                    ("DRC F1 Score", "drc_f1_score", ".4f"),
                    ("DRC False Alarm", "drc_false_alarm", "d")
                ]

                for metric_name, metric_key, fmt in metrics:
                    if metric_key in results:
                        value = results[metric_key]
                        if fmt == "d":
                            report_content.append(f"- {metric_name}: {int(value)}")
                        else:
                            report_content.append(f"- {metric_name}: {value:{fmt}}")

                if "duration" in results:
                    duration_hours = results["duration"] / 3600
                    report_content.append(f"- 训练耗时: {duration_hours:.2f}小时")

            elif results.get("status") == "failed":
                report_content.append(f"**错误码**: {results.get('error_code', 'unknown')}")
            elif results.get("status") == "error":
                report_content.append(f"**错误信息**: {results.get('error', 'unknown')}")

        # 对比分析
        analysis_file = self.base_output_dir / "comparison_analysis.json"
        if analysis_file.exists():
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)

            report_content.append("\n## 对比分析")

            # 最佳/最差性能
            if "metrics_comparison" in analysis:
                report_content.append("\n### 各指标最佳表现")
                for metric, data in analysis["metrics_comparison"].items():
                    if "best" in data:
                        best_exp, best_value = data["best"]
                        report_content.append(f"- **{metric}**: {best_exp} ({best_value:.4f})")

            # 洞察
            if "insights" in analysis and analysis["insights"]:
                report_content.append("\n### 关键洞察")
                for insight in analysis["insights"]:
                    report_content.append(f"- {insight}")

        # 结论和建议
        report_content.append("\n## 结论和建议")
        report_content.append("基于消融实验结果，我们可以得出以下结论：")
        report_content.append("1. 各损失函数组件对模型性能的具体贡献")
        report_content.append("2. 不同损失函数组合的优缺点")
        report_content.append("3. 针对DRC热点检测任务的最优损失函数配置建议")

        # 保存报告
        report_file = self.base_output_dir / "ablation_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        self.logger.info(f"Markdown报告已生成: {report_file}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='DRC热点检测消融实验自动化脚本')

    parser.add_argument('--base_config', required=True,
                       help='基础配置文件路径')
    parser.add_argument('--epochs', type=int, default=20,
                       help='训练轮数 (默认: 20)')
    parser.add_argument('--gpu_ids', default="0",
                       help='GPU设备ID (默认: "0")')
    parser.add_argument('--resume', action='store_true',
                       help='从中断点恢复实验')
    parser.add_argument('--experiments', nargs='+',
                       choices=['rm-VFL', 'rm-FPPL', 'replace-IoU'],
                       help='指定要运行的实验 (默认: 全部)')

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    print("🔬 DRC热点检测消融实验系统")
    print("=" * 60)
    print(f"基础配置: {args.base_config}")
    print(f"训练轮数: {args.epochs}")
    print(f"GPU设备: {args.gpu_ids}")
    print(f"恢复模式: {'是' if args.resume else '否'}")

    # 检查配置文件是否存在
    if not os.path.exists(args.base_config):
        print(f"❌ 配置文件不存在: {args.base_config}")
        return 1

    try:
        # 创建实验管理器
        manager = AblationExperimentManager(
            base_config_path=args.base_config,
            epochs=args.epochs,
            gpu_ids=args.gpu_ids
        )

        # 如果指定了特定实验，则过滤实验列表
        if args.experiments:
            manager.experiments = [exp for exp in manager.experiments
                                 if exp["name"] in args.experiments]
            print(f"指定运行实验: {[exp['name'] for exp in manager.experiments]}")

        # 运行所有实验
        manager.run_all_experiments()

        print("\n🎉 消融实验研究完成！")
        print(f"📊 结果保存在: {manager.base_output_dir}")
        print("📈 查看以下文件获取详细结果:")
        print(f"  - ablation_results.json: 完整实验结果")
        print(f"  - comparison_analysis.json: 对比分析")
        print(f"  - ablation_report.md: Markdown报告")
        print(f"  - ablation_comparison.png: 可视化图表")

        return 0

    except KeyboardInterrupt:
        print("\n⚠️  实验被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 实验过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
