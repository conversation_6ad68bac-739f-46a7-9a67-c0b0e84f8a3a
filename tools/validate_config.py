#!/usr/bin/env python3
"""
配置文件验证脚本
用于验证消融实验配置文件的语法正确性
"""

import os
import sys
import yaml
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from src.core import YAMLConfig


def validate_yaml_syntax(config_path):
    """验证YAML文件语法"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            yaml.safe_load(f)
        print(f"✅ YAML语法验证通过: {config_path}")
        return True
    except yaml.YAMLError as e:
        print(f"❌ YAML语法错误: {config_path}")
        print(f"错误详情: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件读取错误: {config_path}")
        print(f"错误详情: {e}")
        return False


def validate_config_loading(config_path):
    """验证配置文件能否被正确加载"""
    try:
        cfg = YAMLConfig(config_path)
        print(f"✅ 配置加载验证通过: {config_path}")
        
        # 检查关键配置项
        yaml_cfg = cfg.yaml_cfg
        
        # 检查损失函数配置
        if 'SetCriterion' in yaml_cfg:
            criterion_cfg = yaml_cfg['SetCriterion']
            losses = criterion_cfg.get('losses', [])
            weight_dict = criterion_cfg.get('weight_dict', {})
            
            print(f"  📋 损失函数列表: {losses}")
            print(f"  ⚖️  损失权重配置: {weight_dict}")
            
            # 验证损失配置一致性
            if 'vfl' in losses:
                print("  ⚠️  警告: 检测到VFL损失，这可能不是预期的消融实验配置")
            else:
                print("  ✅ VFL损失已移除，符合消融实验要求")
                
        # 检查训练轮数
        epochs = cfg.epoches
        print(f"  🔄 训练轮数: {epochs}")
        
        if epochs == 20:
            print("  ✅ 训练轮数设置正确")
        else:
            print(f"  ⚠️  警告: 训练轮数为{epochs}，预期为20")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {config_path}")
        print(f"错误详情: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='验证配置文件')
    parser.add_argument('config_path', help='配置文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    config_path = Path(args.config_path)
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
        
    print(f"🔍 验证配置文件: {config_path}")
    print("=" * 50)
    
    # 验证YAML语法
    syntax_ok = validate_yaml_syntax(config_path)
    
    if not syntax_ok:
        return False
        
    # 验证配置加载
    loading_ok = validate_config_loading(config_path)
    
    if syntax_ok and loading_ok:
        print("=" * 50)
        print("🎉 配置文件验证通过！")
        return True
    else:
        print("=" * 50)
        print("❌ 配置文件验证失败！")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
