#!/usr/bin/env python3
"""
消融实验配置生成器

该脚本用于生成不同的消融实验配置文件，
支持批量生成和自定义实验设计。

使用方法:
python tools/ablation_config_generator.py --base_config configs/rtdetr/rtdetr_r18vd_6x_coco.yml

作者: DRC消融实验系统
日期: 2024
"""

import os
import sys
import yaml
import argparse
from pathlib import Path
from typing import Dict, List


class AblationConfigGenerator:
    """消融实验配置生成器"""
    
    def __init__(self, base_config_path: str):
        """
        初始化配置生成器
        
        Args:
            base_config_path: 基础配置文件路径
        """
        self.base_config_path = base_config_path
        self.output_dir = Path("./configs/ablation")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载基础配置
        with open(base_config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)
    
    def generate_rm_vfl_config(self) -> str:
        """生成移除VFL的配置"""
        config = self.base_config.copy()
        
        # 修改损失函数配置
        config["SetCriterion"]["losses"] = ["boxes", "fppl"]
        config["SetCriterion"]["weight_dict"] = {
            "loss_bbox": 5,
            "loss_giou": 2,
            "loss_fppl": 1.5
        }
        
        # 修改输出目录
        config["output_dir"] = "./output/ablation/rm-VFL"
        
        # 保存配置
        config_path = self.output_dir / "rm_vfl_config.yml"
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return str(config_path)
    
    def generate_rm_fppl_config(self) -> str:
        """生成移除FPPL的配置"""
        config = self.base_config.copy()
        
        # 修改损失函数配置
        config["SetCriterion"]["losses"] = ["vfl", "boxes"]
        config["SetCriterion"]["weight_dict"] = {
            "loss_vfl": 1,
            "loss_bbox": 5,
            "loss_giou": 2
        }
        
        # 修改输出目录
        config["output_dir"] = "./output/ablation/rm-FPPL"
        
        # 保存配置
        config_path = self.output_dir / "rm_fppl_config.yml"
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return str(config_path)
    
    def generate_replace_iou_config(self) -> str:
        """生成替换IoU损失的配置"""
        config = self.base_config.copy()
        
        # 修改损失函数配置
        config["SetCriterion"]["losses"] = ["vfl", "iou", "fppl"]
        config["SetCriterion"]["weight_dict"] = {
            "loss_vfl": 1,
            "loss_iou": 7,  # 替代bbox+giou的总权重
            "loss_fppl": 1.5
        }
        
        # 修改输出目录
        config["output_dir"] = "./output/ablation/replace-IoU"
        
        # 保存配置
        config_path = self.output_dir / "replace_iou_config.yml"
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return str(config_path)
    
    def generate_weight_ablation_configs(self) -> List[str]:
        """生成权重消融实验配置"""
        configs = []
        
        # FPPL权重消融
        fppl_weights = [0.5, 1.0, 2.0, 3.0]
        for weight in fppl_weights:
            config = self.base_config.copy()
            config["SetCriterion"]["weight_dict"]["loss_fppl"] = weight
            config["output_dir"] = f"./output/ablation/fppl_weight_{weight}"
            
            config_path = self.output_dir / f"fppl_weight_{weight}_config.yml"
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            configs.append(str(config_path))
        
        return configs
    
    def generate_all_configs(self) -> Dict[str, str]:
        """生成所有消融实验配置"""
        configs = {}
        
        print("生成消融实验配置文件...")
        
        # 基本消融实验
        configs["rm-VFL"] = self.generate_rm_vfl_config()
        print(f"✅ 生成移除VFL配置: {configs['rm-VFL']}")
        
        configs["rm-FPPL"] = self.generate_rm_fppl_config()
        print(f"✅ 生成移除FPPL配置: {configs['rm-FPPL']}")
        
        configs["replace-IoU"] = self.generate_replace_iou_config()
        print(f"✅ 生成替换IoU配置: {configs['replace-IoU']}")
        
        # 权重消融实验
        weight_configs = self.generate_weight_ablation_configs()
        for i, config_path in enumerate(weight_configs):
            configs[f"weight-ablation-{i}"] = config_path
            print(f"✅ 生成权重消融配置: {config_path}")
        
        return configs
    
    def generate_experiment_script(self, configs: Dict[str, str]) -> str:
        """生成实验执行脚本"""
        script_content = [
            "#!/bin/bash",
            "# DRC消融实验自动执行脚本",
            "# 该脚本将按顺序执行所有消融实验",
            "",
            "set -e  # 遇到错误时退出",
            "",
            "echo '🔬 开始DRC消融实验'",
            "echo '========================'",
            ""
        ]
        
        for exp_name, config_path in configs.items():
            script_content.extend([
                f"echo '开始实验: {exp_name}'",
                f"python tools/train.py --config {config_path}",
                f"echo '实验 {exp_name} 完成'",
                "echo '------------------------'",
                ""
            ])
        
        script_content.extend([
            "echo '🎉 所有消融实验完成！'",
            "echo '查看结果目录: ./output/ablation/'"
        ])
        
        script_path = self.output_dir / "run_ablation_experiments.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(script_content))
        
        # 添加执行权限
        os.chmod(script_path, 0o755)
        
        return str(script_path)
    
    def generate_comparison_template(self) -> str:
        """生成结果对比模板"""
        template_content = """# DRC消融实验结果对比

## 实验设置
- 基础配置: {base_config}
- 训练轮数: 20
- 评估指标: COCO mAP, DRC Accuracy, DRC Precision, DRC Recall, DRC F1 Score, DRC False Alarm

## 实验结果

### 1. 移除Varifocal Loss (rm-VFL)
- **目的**: 评估VFL对整体检测性能的贡献
- **配置**: 仅使用boxes + fppl损失
- **结果**: 
  - COCO mAP: _____
  - DRC F1 Score: _____
  - 分析: _____

### 2. 移除False Positive Penalty Loss (rm-FPPL)
- **目的**: 评估FPPL对降低误报的效果
- **配置**: 仅使用vfl + boxes损失
- **结果**:
  - DRC Precision: _____
  - DRC False Alarm: _____
  - 分析: _____

### 3. 替换边界框损失 (replace-IoU)
- **目的**: 评估L1+GIoU vs 简单IoU的效果
- **配置**: 使用vfl + iou + fppl损失
- **结果**:
  - DRC Precision: _____
  - COCO mAP: _____
  - 分析: _____

## 总结
1. **最重要的损失组件**: _____
2. **FPPL的贡献**: _____
3. **最优损失函数组合**: _____

## 建议
基于消融实验结果，建议：
1. _____
2. _____
3. _____
""".format(base_config=self.base_config_path)
        
        template_path = self.output_dir / "results_comparison_template.md"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        return str(template_path)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='消融实验配置生成器')
    
    parser.add_argument('--base_config', required=True,
                       help='基础配置文件路径')
    parser.add_argument('--generate_script', action='store_true',
                       help='生成实验执行脚本')
    parser.add_argument('--include_weights', action='store_true',
                       help='包含权重消融实验')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    print("🔧 DRC消融实验配置生成器")
    print("=" * 50)
    
    # 检查基础配置文件
    if not os.path.exists(args.base_config):
        print(f"❌ 基础配置文件不存在: {args.base_config}")
        return 1
    
    try:
        # 创建配置生成器
        generator = AblationConfigGenerator(args.base_config)
        
        # 生成所有配置
        configs = generator.generate_all_configs()
        
        print(f"\n📁 配置文件已生成到: {generator.output_dir}")
        print(f"📊 总共生成 {len(configs)} 个配置文件")
        
        # 生成执行脚本
        if args.generate_script:
            script_path = generator.generate_experiment_script(configs)
            print(f"📜 执行脚本已生成: {script_path}")
            print("   使用方法: bash " + script_path)
        
        # 生成对比模板
        template_path = generator.generate_comparison_template()
        print(f"📋 结果对比模板已生成: {template_path}")
        
        print("\n🎯 下一步:")
        print("1. 检查生成的配置文件")
        print("2. 运行消融实验:")
        print(f"   python tools/ablation_study.py --base_config {args.base_config}")
        print("3. 分析实验结果并填写对比模板")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成配置时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())
