#!/usr/bin/env python3
"""
False Positive Penalty Loss (FPPL) 测试脚本

该脚本用于测试和验证FPPL损失函数的实现，
确保损失计算逻辑正确，并分析其对模型训练的影响。

使用方法:
python tools/test_fppl_loss.py

作者: DRC损失系统
日期: 2024
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from src.zoo.rtdetr.rtdetr_criterion import SetCriterion
from src.zoo.rtdetr.matcher import HungarianMatcher


def create_sample_data():
    """创建测试用的样本数据"""
    batch_size = 2
    num_queries = 100
    num_classes = 1  # DRC热点检测只有1个类别
    
    # 模拟模型输出
    outputs = {
        'pred_logits': torch.randn(batch_size, num_queries, num_classes),
        'pred_boxes': torch.rand(batch_size, num_queries, 4)
    }
    
    # 模拟目标数据
    targets = [
        {
            'labels': torch.tensor([0, 0]),  # 2个热点
            'boxes': torch.tensor([[0.5, 0.5, 0.1, 0.1], [0.3, 0.7, 0.15, 0.12]])
        },
        {
            'labels': torch.tensor([0]),     # 1个热点
            'boxes': torch.tensor([[0.6, 0.4, 0.08, 0.09]])
        }
    ]
    
    return outputs, targets


def test_fppl_loss_basic():
    """测试FPPL损失的基本功能"""
    print("🔍 测试FPPL损失基本功能...")
    
    # 创建损失函数
    matcher = HungarianMatcher(
        weight_dict={'cost_class': 2, 'cost_bbox': 5, 'cost_giou': 2},
        alpha=0.25,
        gamma=2.0
    )
    
    criterion = SetCriterion(
        matcher=matcher,
        weight_dict={'loss_vfl': 1, 'loss_bbox': 5, 'loss_giou': 2, 'loss_fppl': 1.5},
        losses=['vfl', 'boxes', 'fppl'],
        alpha=0.75,
        gamma=2.0,
        num_classes=1
    )
    
    # 创建测试数据
    outputs, targets = create_sample_data()
    
    # 计算损失
    try:
        losses = criterion(outputs, targets)
        
        print("✅ FPPL损失计算成功！")
        print(f"损失项: {list(losses.keys())}")
        
        if 'loss_fppl' in losses:
            print(f"FPPL损失值: {losses['loss_fppl'].item():.6f}")
        
        if 'fppl_high_conf_fp' in losses:
            print(f"高置信度FP数量: {losses['fppl_high_conf_fp']}")
        
        if 'fppl_total_negatives' in losses:
            print(f"总负样本数量: {losses['fppl_total_negatives']}")
            
        return True
        
    except Exception as e:
        print(f"❌ FPPL损失计算失败: {e}")
        return False


def test_fppl_loss_properties():
    """测试FPPL损失的特性"""
    print("\n🔍 测试FPPL损失特性...")
    
    # 创建简化的测试场景
    batch_size = 1
    num_queries = 10
    num_classes = 1
    
    # 场景1：低置信度预测（应该产生较低的FPPL损失）
    print("\n场景1：低置信度预测")
    outputs_low_conf = {
        'pred_logits': torch.full((batch_size, num_queries, num_classes), -2.0),  # 低置信度
        'pred_boxes': torch.rand(batch_size, num_queries, 4)
    }
    
    # 场景2：高置信度预测（应该产生较高的FPPL损失）
    print("场景2：高置信度预测")
    outputs_high_conf = {
        'pred_logits': torch.full((batch_size, num_queries, num_classes), 2.0),   # 高置信度
        'pred_boxes': torch.rand(batch_size, num_queries, 4)
    }
    
    # 无目标的情况（所有预测都是False Positive）
    targets_empty = [{'labels': torch.empty(0, dtype=torch.long), 'boxes': torch.empty(0, 4)}]
    
    matcher = HungarianMatcher(
        weight_dict={'cost_class': 2, 'cost_bbox': 5, 'cost_giou': 2},
        alpha=0.25, gamma=2.0
    )
    
    criterion = SetCriterion(
        matcher=matcher,
        weight_dict={'loss_fppl': 1.0},
        losses=['fppl'],
        alpha=0.75, gamma=2.0, num_classes=1
    )
    
    # 计算两种场景的损失
    try:
        losses_low = criterion(outputs_low_conf, targets_empty)
        losses_high = criterion(outputs_high_conf, targets_empty)
        
        fppl_low = losses_low['loss_fppl'].item()
        fppl_high = losses_high['loss_fppl'].item()
        
        print(f"低置信度FPPL损失: {fppl_low:.6f}")
        print(f"高置信度FPPL损失: {fppl_high:.6f}")
        print(f"损失比值 (高/低): {fppl_high/fppl_low:.2f}")
        
        if fppl_high > fppl_low:
            print("✅ FPPL损失特性正确：高置信度FP产生更高损失")
        else:
            print("❌ FPPL损失特性异常：高置信度FP损失应该更高")
            
        return fppl_high > fppl_low
        
    except Exception as e:
        print(f"❌ FPPL损失特性测试失败: {e}")
        return False


def visualize_fppl_curve():
    """可视化FPPL损失曲线"""
    print("\n📊 生成FPPL损失曲线...")
    
    # 创建置信度范围
    confidence_scores = np.linspace(0.01, 0.99, 100)
    
    # FPPL参数
    alpha = 0.75
    gamma = 2.0
    
    # 计算FPPL损失值
    fppl_values = []
    for conf in confidence_scores:
        one_minus_conf = 1 - conf
        fppl = -alpha * (one_minus_conf ** gamma) * np.log(one_minus_conf)
        # 应用置信度权重
        weighted_fppl = fppl * (conf ** 2)
        fppl_values.append(weighted_fppl)
    
    # 绘制曲线
    plt.figure(figsize=(10, 6))
    plt.plot(confidence_scores, fppl_values, 'b-', linewidth=2, label='FPPL Loss')
    plt.xlabel('Prediction Confidence')
    plt.ylabel('FPPL Loss Value')
    plt.title('False Positive Penalty Loss (FPPL) Curve')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 标注关键点
    key_points = [0.1, 0.3, 0.5, 0.7, 0.9]
    for point in key_points:
        idx = int(point * 100) - 1
        plt.annotate(f'({point:.1f}, {fppl_values[idx]:.3f})', 
                    xy=(point, fppl_values[idx]), 
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    plt.tight_layout()
    plt.savefig('fppl_loss_curve.png', dpi=300, bbox_inches='tight')
    print("✅ FPPL损失曲线已保存为 fppl_loss_curve.png")
    
    # 分析曲线特性
    print("\n📈 FPPL损失曲线分析:")
    print(f"最低损失 (conf=0.01): {fppl_values[0]:.6f}")
    print(f"中等损失 (conf=0.50): {fppl_values[49]:.6f}")
    print(f"最高损失 (conf=0.99): {fppl_values[98]:.6f}")
    print(f"损失增长倍数: {fppl_values[98]/fppl_values[0]:.1f}x")


def test_gradient_flow():
    """测试FPPL损失的梯度流"""
    print("\n🔍 测试FPPL损失梯度流...")
    
    # 创建需要梯度的测试数据
    pred_logits = torch.randn(1, 5, 1, requires_grad=True)
    pred_boxes = torch.rand(1, 5, 4, requires_grad=True)
    
    outputs = {'pred_logits': pred_logits, 'pred_boxes': pred_boxes}
    targets = [{'labels': torch.empty(0, dtype=torch.long), 'boxes': torch.empty(0, 4)}]
    
    matcher = HungarianMatcher(
        weight_dict={'cost_class': 2, 'cost_bbox': 5, 'cost_giou': 2},
        alpha=0.25, gamma=2.0
    )
    
    criterion = SetCriterion(
        matcher=matcher,
        weight_dict={'loss_fppl': 1.0},
        losses=['fppl'],
        alpha=0.75, gamma=2.0, num_classes=1
    )
    
    try:
        losses = criterion(outputs, targets)
        loss = losses['loss_fppl']
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        if pred_logits.grad is not None:
            grad_norm = pred_logits.grad.norm().item()
            print(f"✅ 梯度计算成功，梯度范数: {grad_norm:.6f}")
            
            # 检查梯度是否合理
            if 0 < grad_norm < 100:
                print("✅ 梯度范数在合理范围内")
                return True
            else:
                print(f"⚠️  梯度范数可能异常: {grad_norm}")
                return False
        else:
            print("❌ 梯度计算失败")
            return False
            
    except Exception as e:
        print(f"❌ 梯度流测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始FPPL损失函数测试")
    print("=" * 60)
    
    test_results = []
    
    # 基本功能测试
    test_results.append(test_fppl_loss_basic())
    
    # 损失特性测试
    test_results.append(test_fppl_loss_properties())
    
    # 梯度流测试
    test_results.append(test_gradient_flow())
    
    # 可视化测试
    try:
        visualize_fppl_curve()
        test_results.append(True)
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        test_results.append(False)
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    test_names = ["基本功能", "损失特性", "梯度流", "可视化"]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_rate = sum(test_results) / len(test_results)
    print(f"\n总体成功率: {success_rate:.1%}")
    
    if success_rate == 1.0:
        print("🎉 所有测试通过！FPPL损失函数实现正确。")
    elif success_rate >= 0.75:
        print("⚠️  大部分测试通过，但有部分问题需要修复。")
    else:
        print("❌ 多个测试失败，需要检查FPPL实现。")
    
    print("\n💡 使用建议:")
    print("1. FPPL损失权重建议从1.0开始，根据效果调整到1.5-2.0")
    print("2. 监控训练过程中的Precision和Recall变化")
    print("3. 如果Recall下降过多，适当降低FPPL权重")
    print("4. 观察False Alarm数量的减少情况")


if __name__ == '__main__':
    main()
