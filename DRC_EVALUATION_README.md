# DRC热点检测评估系统

## 🎯 项目概述

本项目实现了专门针对DRC热点检测任务的评估指标系统，完全保留现有的COCO格式目标检测评估指标，同时添加了符合DRC工程应用需求的专用评估器。

## ✨ 核心特性

### 📊 评估指标定义

根据DRC热点检测论文定义，实现以下核心指标：

- **Definition 1 (Accuracy)**: 正确检测的热点数量与真实热点数量的比值 (等同于True Positive Rate/Recall)
- **Definition 2 (False Alarm)**: 被分类器错误检测为热点的非热点区域数量 (等同于False Positive数量)  
- **Definition 3 (F1 Score)**: Precision和Recall的调和平均数

### 🔧 技术实现

- ✅ **完全兼容**: 保留现有COCO格式评估指标（mAP、AP@0.5、AP@0.75等）
- ✅ **并行工作**: 独立的DRC评估器与CocoEvaluator并行运行
- ✅ **可配置**: 支持IoU阈值、热点类别ID等参数配置
- ✅ **自动集成**: 训练验证阶段自动输出DRC专用指标
- ✅ **详细报告**: 提供性能分析和优化建议
- ✅ **可视化**: 集成TensorBoard支持

## 🚀 快速开始

### 1. 配置设置

在配置文件中启用DRC评估：

```yaml
__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../drc_evaluation.yml',  # 添加DRC评估配置
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r50vd.yml',
]
```

### 2. 训练时自动评估

```bash
python tools/train.py --config configs/rtdetr/rtdetr_r18vd_6x_coco.yml
```

### 3. 独立评估测试

```bash
# 基本评估
python tools/test_drc_evaluation.py \
    --config configs/rtdetr/rtdetr_r18vd_6x_coco.yml \
    --checkpoint path/to/checkpoint.pth

# 多IoU阈值分析
python tools/test_drc_evaluation.py \
    --config configs/rtdetr/rtdetr_r18vd_6x_coco.yml \
    --checkpoint path/to/checkpoint.pth \
    --multi_iou
```

### 4. 运行示例

```bash
python examples/drc_evaluation_example.py
```

## 📈 输出结果

### 控制台输出示例

```
============================================================
DRC热点检测评估结果
============================================================
IoU阈值: 0.60
总图像数: 1000

基础统计:
  True Positives (TP): 850
  False Positives (FP): 120
  False Negatives (FN): 50

核心指标 (按论文定义):
  Accuracy (Recall): 0.9444
  False Alarm: 120
  F1 Score: 0.9340

传统指标:
  Precision: 0.8763
  Recall: 0.9444
  FPR (每图像): 0.1200
============================================================
```

### TensorBoard可视化

- `DRC/accuracy`: 准确率（召回率）
- `DRC/false_alarm`: 误报数量  
- `DRC/f1_score`: F1分数
- `DRC/precision`: 精确度
- `DRC/recall`: 召回率
- `DRC_Best/`: 最佳指标记录

## 📁 文件结构

```
├── src/data/coco/coco_eval.py          # DRCHotspotEvaluator核心实现
├── src/solver/det_engine.py            # 评估引擎集成
├── src/solver/det_solver.py            # 训练流程集成
├── configs/drc_evaluation.yml          # DRC评估配置文件
├── tools/test_drc_evaluation.py        # 专用测试脚本
├── examples/drc_evaluation_example.py  # 使用示例
└── docs/DRC_EVALUATION_GUIDE.md        # 详细使用指南
```

## ⚙️ 配置参数

### 基本配置

```yaml
drc_evaluation:
  enabled: true                    # 是否启用DRC评估
  iou_threshold: 0.6              # IoU匹配阈值
  hotspot_class_id: 1             # 热点类别ID
  save_results: true              # 是否保存详细结果
```

### 高级配置

```yaml
drc_evaluation:
  # 多IoU分析
  multi_iou_analysis:
    enabled: false
    thresholds: [0.5, 0.6, 0.7, 0.8]
  
  # TensorBoard集成
  tensorboard:
    enabled: true
    prefix: "DRC"
  
  # 性能分析
  analysis:
    enabled: true
    confidence_analysis: true
```

## 🔍 关键技术细节

### 1. 热点类别过滤
- 只有预测为热点类别的框参与评估
- 避免背景预测影响False Alarm计算
- 确保False Alarm反映真实的热点误检

### 2. IoU匹配算法
- 贪心匹配，按置信度排序
- 每个GT框最多匹配一个预测框
- 每个预测框最多匹配一个GT框

### 3. 指标计算公式
- **Accuracy = TP/(TP+FN)** (等同于Recall)
- **False Alarm = FP数量** (绝对数量，非比率)
- **F1 = 2×(Precision×Recall)/(Precision+Recall)**
- **Precision = TP/(TP+FP)**

## 🛠️ 故障排除

### 常见问题

1. **DRC评估器无输出**: 检查`drc_evaluation.enabled`配置
2. **全部False Positive**: 检查`hotspot_class_id`设置
3. **IoU计算异常**: 确保坐标格式为[x1,y1,x2,y2]

### 性能优化

- 大数据集可设置`save_results=false`节省内存
- 多GPU训练自动同步统计数据
- 注意详细结果文件的磁盘空间占用

## 📚 扩展功能

- 🔄 多IoU阈值对比分析
- 📊 置信度阈值影响分析  
- 🔍 错误案例详细分析
- 📈 自定义评估指标扩展

## 🤝 技术支持

详细文档请参考：`docs/DRC_EVALUATION_GUIDE.md`

如有问题，请检查：
1. 配置文件格式
2. 数据集标注格式
3. 模型输出格式
4. 日志错误信息

---

**注意**: 本系统专门为DRC热点检测任务设计，确保评估逻辑符合DRC工程应用的实际需求。
