'''
by l<PERSON><PERSON><PERSON>
'''
import time 
import json
import datetime

import torch 

from src.misc import dist
from src.data import get_coco_api_from_dataset

from .solver import BaseSolver
from .det_engine import train_one_epoch, evaluate

from torch.utils.tensorboard import SummaryWriter
import os

class DetSolver(BaseSolver):

    def __init__(self, cfg):
        super().__init__(cfg)
        
        # 🔥 TensorBoard初始化 - 确保这段代码存在
        print("🔍 Checking TensorBoard configuration...")
        use_tb = cfg.yaml_cfg.get('use_tensorboard', False)
        log_dir = cfg.yaml_cfg.get('tensorboard_log_dir', './logs')
        
        print(f"  use_tensorboard: {use_tb}")
        print(f"  tensorboard_log_dir: {log_dir}")
        
        if use_tb:
            # 使用绝对路径避免路径问题
            abs_log_dir = os.path.abspath(log_dir)
            os.makedirs(abs_log_dir, exist_ok=True)
            
            # 只在主进程创建writer
            if not dist.is_dist_available_and_initialized() or dist.is_main_process():
                self.writer = SummaryWriter(abs_log_dir)
                print(f"✅ TensorBoard writer created at: {abs_log_dir}")
                
                # 立即写入测试数据
                self.writer.add_scalar('Init/test', 1.0, 0)
                self.writer.flush()
            else:
                self.writer = None
                print("📝 Non-main process, writer disabled")
        else:
            self.writer = None
            print("❌ TensorBoard disabled in config")
    
    def fit(self, ):
        print("Start training")
        self.train()
        # 加载断点，恢复训练状态
        if self.cfg.resume:
            print(f"Resuming training from checkpoint: {self.cfg.resume}")
            self.resume(self.cfg.resume)  # 这里会调用load_state_dict恢复状态

        print("📊 Training setup completed, starting epoch loop...")

        args = self.cfg 
        
        n_parameters = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print('number of params:', n_parameters)

        base_ds = get_coco_api_from_dataset(self.val_dataloader.dataset)
        best_stat = {'epoch': -1, }

        start_time = time.time()
        for epoch in range(self.last_epoch + 1, args.epoches):
            print(f"🔄 Starting epoch {epoch}/{args.epoches}")
            if dist.is_dist_available_and_initialized():
                self.train_dataloader.sampler.set_epoch(epoch)
            
            # 训练一个epoch，返回训练统计
            train_stats = train_one_epoch(
                self.model, self.criterion, self.train_dataloader, self.optimizer, self.device, epoch,
                args.clip_max_norm, print_freq=args.log_step, ema=self.ema, scaler=self.scaler)
            
            # 🔥 记录训练损失到TensorBoard
            if self.writer:
                for k, v in train_stats.items():
                    value = v.item() if hasattr(v, 'item') else v
                    self.writer.add_scalar(f'Train/{k}', value, epoch)
            
            print(f"✅ Epoch {epoch} completed, recording to TensorBoard...")

            self.lr_scheduler.step()
            
            # 🔥 记录学习率
            if self.writer:
                print(f"📝 Writing epoch {epoch} data to TensorBoard")
                current_lr = self.optimizer.param_groups[0]['lr']
                self.writer.add_scalar('Learning_Rate', current_lr, epoch)

            if self.output_dir:
                checkpoint_paths = [self.output_dir / 'checkpoint.pth']
                if (epoch + 1) % args.checkpoint_step == 0:
                    checkpoint_paths.append(self.output_dir / f'checkpoint{epoch:04}.pth')
                for checkpoint_path in checkpoint_paths:
                    dist.save_on_master(self.state_dict(epoch), checkpoint_path)

            module = self.ema.module if self.ema else self.model
            test_stats, coco_evaluator = evaluate(
                module, self.criterion, self.postprocessor, self.val_dataloader, base_ds, self.device, self.output_dir
            )

            # 🔥 记录验证统计到TensorBoard
            if self.writer:
                for k, v in test_stats.items():
                    if isinstance(v, (list, tuple)) and len(v) > 0:
                        value = v[0]
                    else:
                        value = v
                    self.writer.add_scalar(f'Val/{k}', value, epoch)

            # 更新最佳统计
            for k in test_stats.keys():
                if k in best_stat:
                    best_stat['epoch'] = epoch if test_stats[k][0] > best_stat[k] else best_stat['epoch']
                    best_stat[k] = max(best_stat[k], test_stats[k][0])
                else:
                    best_stat['epoch'] = epoch
                    best_stat[k] = test_stats[k][0]
            print('best_stat: ', best_stat)

            log_stats = {**{f'train_{k}': v for k, v in train_stats.items()},
                        **{f'test_{k}': v for k, v in test_stats.items()},
                        'epoch': epoch,
                        'n_parameters': n_parameters}

            if self.output_dir and dist.is_main_process():
                with (self.output_dir / "log.txt").open("a") as f:
                    f.write(json.dumps(log_stats) + "\n")

                # for evaluation logs
                if coco_evaluator is not None:
                    (self.output_dir / 'eval').mkdir(exist_ok=True)
                    if "bbox" in coco_evaluator.coco_eval:
                        filenames = ['latest.pth']
                        if epoch % 50 == 0:
                            filenames.append(f'{epoch:03}.pth')
                        for name in filenames:
                            torch.save(coco_evaluator.coco_eval["bbox"].eval,
                                    self.output_dir / "eval" / name)

        # 训练结束关闭TensorBoard writer
        if self.writer:
            self.writer.close()
            print("TensorBoard writer closed")

        total_time = time.time() - start_time
        total_time_str = str(datetime.timedelta(seconds=int(total_time)))
        print('Training time {}'.format(total_time_str))

    def val(self, ):
        self.eval()

        base_ds = get_coco_api_from_dataset(self.val_dataloader.dataset)
        
        module = self.ema.module if self.ema else self.model
        test_stats, coco_evaluator = evaluate(module, self.criterion, self.postprocessor,
                self.val_dataloader, base_ds, self.device, self.output_dir)
                
        if self.output_dir:
            dist.save_on_master(coco_evaluator.coco_eval["bbox"].eval, self.output_dir / "eval.pth")
        
        return test_stats, coco_evaluator

    def resume(self, path):
        '''加载断点，恢复训练状态'''
        state = torch.load(path, map_location='cpu')
        self.load_state_dict(state)
        print(f"Loaded checkpoint from {path}, resuming from epoch {self.last_epoch}")
