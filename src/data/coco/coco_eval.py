# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
"""
COCO evaluator that works in distributed mode.

Mostly copy-paste from https://github.com/pytorch/vision/blob/edfd5a7/references/detection/coco_eval.py
The difference is that there is less copy-pasting from pycocotools
in the end of the file, as python3 can suppress prints with contextlib
"""
import os
import contextlib
import copy
import numpy as np
import torch
import json
from collections import defaultdict

from pycocotools.cocoeval import COCOeval
from pycocotools.coco import COCO
import pycocotools.mask as mask_util

from src.misc import dist


__all__ = ['CocoEvaluator', 'DRCHotspotEvaluator']


class CocoEvaluator(object):
    def __init__(self, coco_gt, iou_types):
        assert isinstance(iou_types, (list, tuple))
        coco_gt = copy.deepcopy(coco_gt)
        self.coco_gt = coco_gt

        self.iou_types = iou_types
        self.coco_eval = {}
        for iou_type in iou_types:
            self.coco_eval[iou_type] = COCOeval(coco_gt, iouType=iou_type)

        self.img_ids = []
        self.eval_imgs = {k: [] for k in iou_types}

    def update(self, predictions):
        img_ids = list(np.unique(list(predictions.keys())))
        self.img_ids.extend(img_ids)

        for iou_type in self.iou_types:
            results = self.prepare(predictions, iou_type)

            # suppress pycocotools prints
            with open(os.devnull, 'w') as devnull:
                with contextlib.redirect_stdout(devnull):
                    coco_dt = COCO.loadRes(self.coco_gt, results) if results else COCO()
            coco_eval = self.coco_eval[iou_type]

            coco_eval.cocoDt = coco_dt
            coco_eval.params.imgIds = list(img_ids)
            img_ids, eval_imgs = evaluate(coco_eval)

            self.eval_imgs[iou_type].append(eval_imgs)

    def synchronize_between_processes(self):
        for iou_type in self.iou_types:
            self.eval_imgs[iou_type] = np.concatenate(self.eval_imgs[iou_type], 2)
            create_common_coco_eval(self.coco_eval[iou_type], self.img_ids, self.eval_imgs[iou_type])

    def accumulate(self):
        for coco_eval in self.coco_eval.values():
            coco_eval.accumulate()

    def summarize(self):
        for iou_type, coco_eval in self.coco_eval.items():
            print("IoU metric: {}".format(iou_type))
            coco_eval.summarize()

    def prepare(self, predictions, iou_type):
        if iou_type == "bbox":
            return self.prepare_for_coco_detection(predictions)
        elif iou_type == "segm":
            return self.prepare_for_coco_segmentation(predictions)
        elif iou_type == "keypoints":
            return self.prepare_for_coco_keypoint(predictions)
        else:
            raise ValueError("Unknown iou type {}".format(iou_type))

    def prepare_for_coco_detection(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue

            boxes = prediction["boxes"]
            boxes = convert_to_xywh(boxes).tolist()
            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()

            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        "bbox": box,
                        "score": scores[k],
                    }
                    for k, box in enumerate(boxes)
                ]
            )
        return coco_results

    def prepare_for_coco_segmentation(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue

            scores = prediction["scores"]
            labels = prediction["labels"]
            masks = prediction["masks"]

            masks = masks > 0.5

            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()

            rles = [
                mask_util.encode(np.array(mask[0, :, :, np.newaxis], dtype=np.uint8, order="F"))[0]
                for mask in masks
            ]
            for rle in rles:
                rle["counts"] = rle["counts"].decode("utf-8")

            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        "segmentation": rle,
                        "score": scores[k],
                    }
                    for k, rle in enumerate(rles)
                ]
            )
        return coco_results

    def prepare_for_coco_keypoint(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue

            boxes = prediction["boxes"]
            boxes = convert_to_xywh(boxes).tolist()
            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()
            keypoints = prediction["keypoints"]
            keypoints = keypoints.flatten(start_dim=1).tolist()

            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        'keypoints': keypoint,
                        "score": scores[k],
                    }
                    for k, keypoint in enumerate(keypoints)
                ]
            )
        return coco_results


def convert_to_xywh(boxes):
    xmin, ymin, xmax, ymax = boxes.unbind(1)
    return torch.stack((xmin, ymin, xmax - xmin, ymax - ymin), dim=1)


def merge(img_ids, eval_imgs):
    all_img_ids = dist.all_gather(img_ids)
    all_eval_imgs = dist.all_gather(eval_imgs)

    merged_img_ids = []
    for p in all_img_ids:
        merged_img_ids.extend(p)

    merged_eval_imgs = []
    for p in all_eval_imgs:
        merged_eval_imgs.append(p)

    merged_img_ids = np.array(merged_img_ids)
    merged_eval_imgs = np.concatenate(merged_eval_imgs, 2)

    # keep only unique (and in sorted order) images
    merged_img_ids, idx = np.unique(merged_img_ids, return_index=True)
    merged_eval_imgs = merged_eval_imgs[..., idx]

    return merged_img_ids, merged_eval_imgs


def create_common_coco_eval(coco_eval, img_ids, eval_imgs):
    img_ids, eval_imgs = merge(img_ids, eval_imgs)
    img_ids = list(img_ids)
    eval_imgs = list(eval_imgs.flatten())

    coco_eval.evalImgs = eval_imgs
    coco_eval.params.imgIds = img_ids
    coco_eval._paramsEval = copy.deepcopy(coco_eval.params)


#################################################################
# From pycocotools, just removed the prints and fixed
# a Python3 bug about unicode not defined
#################################################################


# import io
# from contextlib import redirect_stdout
# def evaluate(imgs):
#     with redirect_stdout(io.StringIO()):
#         imgs.evaluate()
#     return imgs.params.imgIds, np.asarray(imgs.evalImgs).reshape(-1, len(imgs.params.areaRng), len(imgs.params.imgIds))


def evaluate(self):
    '''
    Run per image evaluation on given images and store results (a list of dict) in self.evalImgs
    :return: None
    '''
    # tic = time.time()
    # print('Running per image evaluation...')
    p = self.params
    # add backward compatibility if useSegm is specified in params
    if p.useSegm is not None:
        p.iouType = 'segm' if p.useSegm == 1 else 'bbox'
        print('useSegm (deprecated) is not None. Running {} evaluation'.format(p.iouType))
    # print('Evaluate annotation type *{}*'.format(p.iouType))
    p.imgIds = list(np.unique(p.imgIds))
    if p.useCats:
        p.catIds = list(np.unique(p.catIds))
    p.maxDets = sorted(p.maxDets)
    self.params = p

    self._prepare()
    # loop through images, area range, max detection number
    catIds = p.catIds if p.useCats else [-1]

    if p.iouType == 'segm' or p.iouType == 'bbox':
        computeIoU = self.computeIoU
    elif p.iouType == 'keypoints':
        computeIoU = self.computeOks
    self.ious = {
        (imgId, catId): computeIoU(imgId, catId)
        for imgId in p.imgIds
        for catId in catIds}

    evaluateImg = self.evaluateImg
    maxDet = p.maxDets[-1]
    evalImgs = [
        evaluateImg(imgId, catId, areaRng, maxDet)
        for catId in catIds
        for areaRng in p.areaRng
        for imgId in p.imgIds
    ]
    # this is NOT in the pycocotools code, but could be done outside
    evalImgs = np.asarray(evalImgs).reshape(len(catIds), len(p.areaRng), len(p.imgIds))
    self._paramsEval = copy.deepcopy(self.params)
    # toc = time.time()
    # print('DONE (t={:0.2f}s).'.format(toc-tic))
    return p.imgIds, evalImgs

#################################################################
# end of straight copy from pycocotools, just removing the prints
#################################################################


class DRCHotspotEvaluator:
    """
    专门针对DRC热点检测任务的评估器

    根据论文定义实现以下指标：
    - Definition 1 (Accuracy): 正确检测的热点数量与真实热点数量的比值 (等同于Recall)
    - Definition 2 (False Alarm): 被分类器错误检测为热点的非热点区域数量 (等同于FP数量)
    - Definition 3 (F1 Score): Precision和Recall的调和平均数
    """

    def __init__(self, iou_threshold=0.6, hotspot_class_id=1, save_results=True, confidence_threshold=0.6):
        """
        初始化DRC热点检测评估器

        Args:
            iou_threshold (float): IoU阈值，默认0.6
            hotspot_class_id (int): 热点类别ID，默认1（背景为0）
            save_results (bool): 是否保存详细结果，默认True
            confidence_threshold (float): 置信度阈值，默认0.6
        """
        self.iou_threshold = iou_threshold
        self.hotspot_class_id = hotspot_class_id
        self.save_results = save_results
        self.confidence_threshold = confidence_threshold

        # 重置统计数据
        self.reset()

    def reset(self):
        """重置所有统计数据"""
        self.true_positives = 0
        self.false_positives = 0  # False Alarm
        self.false_negatives = 0

        # 详细结果存储
        self.detailed_results = []
        self.per_image_stats = {}

    def calculate_iou(self, box1, box2):
        """
        计算两个边界框的IoU

        Args:
            box1, box2: [x1, y1, x2, y2] 格式的边界框

        Returns:
            float: IoU值
        """
        # 计算交集区域
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # 计算并集区域
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def match_predictions_to_ground_truth(self, pred_boxes, pred_scores, pred_labels, gt_boxes):
        """
        将预测框与ground truth框进行匹配

        Args:
            pred_boxes: 预测边界框 [N, 4] (x1, y1, x2, y2)
            pred_scores: 预测置信度 [N]
            pred_labels: 预测类别 [N]
            gt_boxes: ground truth边界框 [M, 4] (x1, y1, x2, y2)

        Returns:
            dict: 包含匹配结果的字典
        """
        # 只考虑预测为热点类别且置信度超过阈值的框
        hotspot_mask = (pred_labels == self.hotspot_class_id) & (pred_scores >= self.confidence_threshold)
        hotspot_pred_boxes = pred_boxes[hotspot_mask]
        hotspot_pred_scores = pred_scores[hotspot_mask]

        # 按置信度排序
        if len(hotspot_pred_scores) > 0:
            sorted_indices = torch.argsort(hotspot_pred_scores, descending=True)
            hotspot_pred_boxes = hotspot_pred_boxes[sorted_indices]
            hotspot_pred_scores = hotspot_pred_scores[sorted_indices]

        # 初始化匹配结果
        gt_matched = torch.zeros(len(gt_boxes), dtype=torch.bool)
        pred_matched = torch.zeros(len(hotspot_pred_boxes), dtype=torch.bool)

        true_positive_pairs = []

        # 对每个预测框寻找最佳匹配的GT框
        for pred_idx, pred_box in enumerate(hotspot_pred_boxes):
            best_iou = 0.0
            best_gt_idx = -1

            for gt_idx, gt_box in enumerate(gt_boxes):
                if gt_matched[gt_idx]:
                    continue

                iou = self.calculate_iou(pred_box.cpu().numpy(), gt_box.cpu().numpy())
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx

            # 如果IoU超过阈值且GT框未被匹配，则为True Positive
            if best_iou >= self.iou_threshold and best_gt_idx != -1:
                gt_matched[best_gt_idx] = True
                pred_matched[pred_idx] = True
                true_positive_pairs.append((pred_idx, best_gt_idx, best_iou))

        return {
            'true_positive_pairs': true_positive_pairs,
            'pred_matched': pred_matched,
            'gt_matched': gt_matched,
            'hotspot_pred_boxes': hotspot_pred_boxes,
            'hotspot_pred_scores': hotspot_pred_scores
        }

    def update(self, predictions):
        """
        更新评估统计数据

        Args:
            predictions: 字典，格式为 {image_id: {'boxes': tensor, 'scores': tensor, 'labels': tensor}}
        """
        for image_id, prediction in predictions.items():
            if len(prediction['boxes']) == 0:
                continue

            pred_boxes = prediction['boxes']
            pred_scores = prediction['scores']
            pred_labels = prediction['labels']

            # 获取对应的ground truth（需要从数据集中获取）
            # 这里假设ground truth已经通过某种方式提供
            # 在实际使用中，需要从COCO数据集中获取对应图像的GT
            gt_boxes = prediction.get('gt_boxes', torch.empty(0, 4))

            if len(gt_boxes) == 0:
                # 如果没有GT框，所有热点预测都是False Positive
                hotspot_mask = pred_labels == self.hotspot_class_id
                self.false_positives += hotspot_mask.sum().item()
                continue

            # 进行匹配
            match_result = self.match_predictions_to_ground_truth(
                pred_boxes, pred_scores, pred_labels, gt_boxes
            )

            # 更新统计数据
            tp_count = len(match_result['true_positive_pairs'])
            fp_count = (~match_result['pred_matched']).sum().item()
            fn_count = (~match_result['gt_matched']).sum().item()

            self.true_positives += tp_count
            self.false_positives += fp_count
            self.false_negatives += fn_count

            # 保存详细结果
            if self.save_results:
                self.per_image_stats[image_id] = {
                    'tp': tp_count,
                    'fp': fp_count,
                    'fn': fn_count,
                    'true_positive_pairs': match_result['true_positive_pairs'],
                    'num_gt': len(gt_boxes),
                    'num_pred_hotspots': len(match_result['hotspot_pred_boxes'])
                }

    def compute_metrics(self):
        """
        计算DRC热点检测评估指标

        Returns:
            dict: 包含所有评估指标的字典
        """
        # 基础统计
        tp = self.true_positives
        fp = self.false_positives  # False Alarm
        fn = self.false_negatives

        # 计算指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0  # Accuracy in paper
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

        # False Positive Rate (误报率)
        # 注意：在目标检测中，通常没有True Negative的概念，所以FPR的计算需要特殊处理
        # 这里我们将FPR定义为每张图像的平均False Positive数量
        total_images = len(self.per_image_stats) if self.per_image_stats else 1
        fpr = fp / total_images

        metrics = {
            # 基础统计
            'true_positives': tp,
            'false_positives': fp,  # False Alarm
            'false_negatives': fn,

            # 核心指标（按论文定义）
            'accuracy': recall,  # Definition 1: 等同于Recall
            'false_alarm': fp,   # Definition 2: False Alarm数量
            'f1_score': f1_score,  # Definition 3: F1 Score

            # 传统指标
            'precision': precision,
            'recall': recall,
            'fpr': fpr,  # False Positive Rate

            # 额外统计
            'total_images': total_images,
            'iou_threshold': self.iou_threshold
        }

        return metrics

    def summarize(self):
        """输出评估结果摘要"""
        metrics = self.compute_metrics()

        print("\n" + "="*60)
        print("DRC热点检测评估结果")
        print("="*60)
        print(f"IoU阈值: {metrics['iou_threshold']:.2f}")
        print(f"总图像数: {metrics['total_images']}")
        print()

        print("基础统计:")
        print(f"  True Positives (TP): {metrics['true_positives']}")
        print(f"  False Positives (FP): {metrics['false_positives']}")
        print(f"  False Negatives (FN): {metrics['false_negatives']}")
        print()

        print("核心指标 (按论文定义):")
        print(f"  Accuracy (Recall): {metrics['accuracy']:.4f}")
        print(f"  False Alarm: {metrics['false_alarm']}")
        print(f"  F1 Score: {metrics['f1_score']:.4f}")
        print()

        print("传统指标:")
        print(f"  Precision: {metrics['precision']:.4f}")
        print(f"  Recall: {metrics['recall']:.4f}")
        print(f"  FPR (每图像): {metrics['fpr']:.4f}")
        print("="*60)

        return metrics

    def save_detailed_results(self, output_path):
        """
        保存详细评估结果到JSON文件

        Args:
            output_path (str): 输出文件路径
        """
        if not self.save_results:
            print("Warning: save_results was disabled during initialization")
            return

        # 转换per_image_stats中的numpy数组为列表，以便JSON序列化
        serializable_stats = {}
        for image_id, stats in self.per_image_stats.items():
            serializable_stats[str(image_id)] = {
                'tp': int(stats['tp']),
                'fp': int(stats['fp']),
                'fn': int(stats['fn']),
                'num_gt': int(stats['num_gt']),
                'num_pred_hotspots': int(stats['num_pred_hotspots']),
                'true_positive_pairs': [(int(pred_idx), int(gt_idx), float(iou))
                                      for pred_idx, gt_idx, iou in stats['true_positive_pairs']]
            }

        results = {
            'metrics': self.compute_metrics(),
            'per_image_stats': serializable_stats,
            'evaluation_config': {
                'iou_threshold': self.iou_threshold,
                'hotspot_class_id': self.hotspot_class_id
            }
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"详细评估结果已保存到: {output_path}")

    def update_with_coco_gt(self, predictions, coco_gt):
        """
        使用COCO格式的ground truth更新评估统计

        Args:
            predictions: 预测结果字典
            coco_gt: COCO格式的ground truth对象
        """
        for image_id, prediction in predictions.items():
            if len(prediction['boxes']) == 0:
                continue

            pred_boxes = prediction['boxes']
            pred_scores = prediction['scores']
            pred_labels = prediction['labels']

            # 从COCO GT中获取对应图像的标注
            ann_ids = coco_gt.getAnnIds(imgIds=[image_id])
            anns = coco_gt.loadAnns(ann_ids)

            # 提取GT框（只考虑热点类别）
            gt_boxes = []
            for ann in anns:
                if ann['category_id'] == self.hotspot_class_id:
                    bbox = ann['bbox']  # [x, y, w, h] format
                    # 转换为 [x1, y1, x2, y2] format
                    x1, y1, w, h = bbox
                    x2, y2 = x1 + w, y1 + h
                    gt_boxes.append([x1, y1, x2, y2])

            gt_boxes = torch.tensor(gt_boxes, dtype=torch.float32) if gt_boxes else torch.empty(0, 4)

            if len(gt_boxes) == 0:
                # 如果没有GT框，所有高置信度热点预测都是False Positive
                hotspot_mask = (pred_labels == self.hotspot_class_id) & (pred_scores >= self.confidence_threshold)
                self.false_positives += hotspot_mask.sum().item()
                continue

            # 进行匹配
            match_result = self.match_predictions_to_ground_truth(
                pred_boxes, pred_scores, pred_labels, gt_boxes
            )

            # 更新统计数据
            tp_count = len(match_result['true_positive_pairs'])
            # 🔥 修复关键逻辑错误：FP应该只计算热点类别的未匹配预测框
            fp_count = (~match_result['pred_matched']).sum().item()  # 这里已经是热点预测框的未匹配数量
            fn_count = (~match_result['gt_matched']).sum().item()

            self.true_positives += tp_count
            self.false_positives += fp_count
            self.false_negatives += fn_count

            # 保存详细结果
            if self.save_results:
                self.per_image_stats[image_id] = {
                    'tp': tp_count,
                    'fp': fp_count,
                    'fn': fn_count,
                    'true_positive_pairs': match_result['true_positive_pairs'],
                    'num_gt': len(gt_boxes),
                    'num_pred_hotspots': len(match_result['hotspot_pred_boxes'])
                }

    def synchronize_between_processes(self):
        """在分布式训练中同步统计数据"""
        if not dist.is_dist_available_and_initialized():
            return

        # 同步基础统计数据
        stats = torch.tensor([self.true_positives, self.false_positives, self.false_negatives],
                           dtype=torch.long, device='cuda')

        # 使用torch.distributed直接进行all_reduce
        import torch.distributed as tdist
        tdist.all_reduce(stats)

        self.true_positives = stats[0].item()
        self.false_positives = stats[1].item()
        self.false_negatives = stats[2].item()

        # 注意：per_image_stats的同步比较复杂，这里暂时跳过
        # 在实际应用中，可能需要收集所有进程的详细统计数据

