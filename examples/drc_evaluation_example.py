#!/usr/bin/env python3
"""
DRC热点检测评估系统使用示例

这个示例展示了如何使用DRC热点检测评估器进行独立评估。
"""

import torch
import numpy as np
from src.data.coco.coco_eval import DRCHotspotEvaluator


def create_sample_data():
    """创建示例数据用于测试"""
    # 模拟预测结果
    predictions = {
        1: {  # image_id = 1
            'boxes': torch.tensor([
                [10, 10, 50, 50],    # 预测框1
                [60, 60, 100, 100],  # 预测框2
                [200, 200, 240, 240] # 预测框3
            ], dtype=torch.float32),
            'scores': torch.tensor([0.9, 0.8, 0.7], dtype=torch.float32),
            'labels': torch.tensor([1, 1, 1], dtype=torch.long),  # 都预测为热点
            'gt_boxes': torch.tensor([
                [12, 12, 48, 48],    # GT框1 - 与预测框1匹配
                [150, 150, 190, 190] # GT框2 - 无匹配的预测框
            ], dtype=torch.float32)
        },
        2: {  # image_id = 2
            'boxes': torch.tensor([
                [20, 20, 60, 60],    # 预测框1
                [80, 80, 120, 120],  # 预测框2
            ], dtype=torch.float32),
            'scores': torch.tensor([0.85, 0.75], dtype=torch.float32),
            'labels': torch.tensor([1, 0], dtype=torch.long),  # 一个热点，一个背景
            'gt_boxes': torch.tensor([
                [22, 22, 58, 58],    # GT框1 - 与预测框1匹配
            ], dtype=torch.float32)
        }
    }
    
    return predictions


def main():
    """主函数"""
    print("DRC热点检测评估系统示例")
    print("=" * 50)
    
    # 创建评估器
    evaluator = DRCHotspotEvaluator(
        iou_threshold=0.6,
        hotspot_class_id=1,
        save_results=True
    )
    
    # 创建示例数据
    predictions = create_sample_data()
    
    print("示例数据:")
    for image_id, pred in predictions.items():
        print(f"  图像 {image_id}:")
        print(f"    预测框数量: {len(pred['boxes'])}")
        print(f"    GT框数量: {len(pred['gt_boxes'])}")
        print(f"    热点预测数量: {(pred['labels'] == 1).sum().item()}")
    
    # 更新评估器
    print("\n开始评估...")
    evaluator.update(predictions)
    
    # 输出结果
    metrics = evaluator.summarize()
    
    # 保存详细结果
    evaluator.save_detailed_results("example_drc_results.json")
    
    print("\n评估完成！")
    print("详细结果已保存到: example_drc_results.json")
    
    # 分析结果
    print("\n结果分析:")
    print(f"- 在IoU阈值{evaluator.iou_threshold}下:")
    print(f"  * 正确检测的热点: {metrics['true_positives']}")
    print(f"  * 误报的热点: {metrics['false_positives']}")
    print(f"  * 遗漏的热点: {metrics['false_negatives']}")
    print(f"  * 准确率(召回率): {metrics['accuracy']:.4f}")
    print(f"  * 精确度: {metrics['precision']:.4f}")
    print(f"  * F1分数: {metrics['f1_score']:.4f}")
    
    # 性能建议
    if metrics['precision'] < 0.8:
        print("\n💡 建议: 精确度较低，考虑提高检测阈值以减少误报")
    if metrics['recall'] < 0.8:
        print("\n💡 建议: 召回率较低，考虑降低检测阈值以减少遗漏")
    if metrics['f1_score'] > 0.9:
        print("\n🎉 优秀: F1分数很高，模型性能良好！")


if __name__ == '__main__':
    main()
