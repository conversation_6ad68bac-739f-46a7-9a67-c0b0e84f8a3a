# DRC热点检测专用损失函数配置
# 包含False Positive Penalty Loss (FPPL)的完整配置

# 损失函数权重配置
drc_loss_weights:
  # 基础损失权重
  loss_vfl: 1.0      # Varifocal Loss - 分类损失
  loss_bbox: 5.0     # L1 边界框回归损失  
  loss_giou: 2.0     # 广义IoU损失
  
  # 🔥 DRC专用损失
  loss_fppl: 1.5     # False Positive Penalty Loss
  
  # 说明：
  # - loss_fppl权重1.5是经过调优的平衡值
  # - 较高值会降低FP但可能略微降低Recall
  # - 可根据实际需求调整：
  #   * 1.0-1.5: 平衡精确度和召回率
  #   * 1.5-2.0: 更注重降低误报
  #   * 0.5-1.0: 更注重保持召回率

# FPPL损失函数参数
fppl_config:
  # 焦点损失参数
  alpha: 0.75        # 类别平衡参数，与VFL保持一致
  gamma: 2.0         # 难样本聚焦参数，与VFL保持一致
  
  # 置信度权重策略
  confidence_weight_power: 2.0  # 置信度权重的幂次，默认为2(平方)
  
  # 数值稳定性参数
  eps: 1e-8          # 避免log(0)的小值
  
  # 损失应用策略
  apply_to_classes: [0]  # 应用FPPL的类别索引，[0]表示热点类别
  
  # 高置信度阈值（用于统计）
  high_confidence_threshold: 0.5

# 损失函数组合策略
loss_combination:
  # 基础损失
  base_losses: ['vfl', 'boxes']  # 必须包含的损失
  
  # DRC专用损失
  drc_losses: ['fppl']           # DRC热点检测专用损失
  
  # 总损失公式：
  # L_total = w_vfl × L_vfl + w_bbox × L_bbox + w_giou × L_giou + w_fppl × L_fppl + L_aux + L_dn

# 训练阶段配置
training_stages:
  # 阶段1：基础训练（前30%的epoch）
  stage1:
    epoch_ratio: 0.3
    fppl_weight_multiplier: 0.5  # FPPL权重乘数，逐步引入
    description: "基础特征学习阶段，FPPL权重较低"
  
  # 阶段2：平衡训练（中间40%的epoch）  
  stage2:
    epoch_ratio: 0.4
    fppl_weight_multiplier: 1.0  # 正常FPPL权重
    description: "平衡训练阶段，正常FPPL权重"
  
  # 阶段3：精细调优（最后30%的epoch）
  stage3:
    epoch_ratio: 0.3
    fppl_weight_multiplier: 1.2  # 略微增加FPPL权重
    description: "精细调优阶段，强化False Positive抑制"

# 自适应权重调整
adaptive_weighting:
  enabled: false  # 是否启用自适应权重调整
  
  # 基于验证集性能的权重调整
  validation_based:
    # 当Precision低于阈值时，增加FPPL权重
    precision_threshold: 0.95
    fppl_weight_increase: 0.1
    
    # 当Recall低于阈值时，减少FPPL权重  
    recall_threshold: 0.95
    fppl_weight_decrease: 0.1
    
    # 权重调整的最大最小值
    fppl_weight_min: 0.5
    fppl_weight_max: 3.0

# 损失监控和日志
loss_monitoring:
  # 是否记录详细的损失统计
  detailed_logging: true
  
  # 记录的统计项
  log_items:
    - "fppl_high_conf_fp"      # 高置信度False Positive数量
    - "fppl_total_negatives"   # 总负样本数量
    - "fppl_loss_value"        # FPPL损失值
    - "precision_recall_ratio" # 精确度召回率比值
  
  # TensorBoard可视化
  tensorboard:
    enabled: true
    loss_prefix: "Loss"
    stats_prefix: "Stats"

# 实验配置模板
experiment_templates:
  # 保守配置：优先保证召回率
  conservative:
    loss_fppl: 0.8
    fppl_alpha: 0.5
    description: "保守配置，优先保证高召回率"
  
  # 平衡配置：平衡精确度和召回率
  balanced:
    loss_fppl: 1.5
    fppl_alpha: 0.75
    description: "平衡配置，兼顾精确度和召回率"
  
  # 激进配置：优先降低误报
  aggressive:
    loss_fppl: 2.5
    fppl_alpha: 0.9
    description: "激进配置，优先降低False Positive"

# 使用说明
usage_notes: |
  1. FPPL损失专门针对DRC热点检测任务设计
  2. 通过惩罚高置信度的False Positive来提升Precision
  3. 权重建议：
     - 初始训练：loss_fppl = 1.0
     - 精细调优：loss_fppl = 1.5-2.0
     - 如果Recall下降过多，适当降低权重
  4. 监控指标：
     - 重点关注F1 Score的变化
     - 平衡Precision和Recall的权衡
     - 观察False Alarm的减少情况
