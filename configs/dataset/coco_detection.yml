task: detection

num_classes: 2
remap_mscoco_category: False

train_dataloader: 
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: "/data/share/PublicDatasets/NEW/DRC-DETR/train/layout/"
    ann_file: "/data/share/PublicDatasets/NEW/DRC-DETR/train/annotations_drc.json"
    transforms:
      type: Compose
      ops: ~
  shuffle: True
  batch_size: 64
  num_workers: 16
  drop_last: True 


val_dataloader:
  type: DataLoader
  dataset: 
    type: CocoDetection
    img_folder: "/data/share/PublicDatasets/NEW/DRC-DETR/test/layout/"
    ann_file: "/data/share/PublicDatasets/NEW/DRC-DETR/test/annotations_drc.json"
    transforms:
      type: Compose
      ops: ~ 

  shuffle: False
  batch_size: 64
  num_workers: 16
  drop_last: False