# DRC热点检测评估配置文件
# 可以被其他配置文件包含使用

# DRC热点检测专用评估器配置
drc_evaluation:
  # 是否启用DRC评估器（与COCO评估器并行工作）
  enabled: true
  
  # IoU阈值配置
  # 默认使用0.6，这是DRC热点检测的常用阈值
  # 可以配置多个阈值进行对比分析
  iou_threshold: 0.6
  
  # 多IoU阈值对比分析（可选）
  # 如果启用，将在多个IoU阈值下计算指标
  multi_iou_analysis:
    enabled: false
    thresholds: [0.5, 0.6, 0.7, 0.8]
  
  # 类别配置
  # 热点类别ID（通常为1，背景为0）
  hotspot_class_id: 1

  # 置信度阈值配置
  # 只考虑置信度超过此值的预测框，避免低质量预测影响评估
  confidence_threshold: 0.5
  
  # 结果保存配置
  save_results: true
  # 详细结果文件名（相对于output_dir）
  results_filename: "drc_evaluation_results.json"
  
  # 输出配置
  # 是否在控制台输出详细评估结果
  verbose: true
  # 是否输出每张图像的详细统计
  per_image_stats: true
  
  # TensorBoard集成配置
  tensorboard:
    # 是否将DRC指标记录到TensorBoard
    enabled: true
    # 指标前缀
    prefix: "DRC"
  
  # 性能分析配置
  analysis:
    # 是否生成性能分析报告
    enabled: true
    # 是否分析不同置信度阈值下的性能
    confidence_analysis: true
    confidence_thresholds: [0.1, 0.3, 0.5, 0.7, 0.9]
    
  # 错误分析配置
  error_analysis:
    # 是否进行错误分析
    enabled: true
    # 是否保存False Positive和False Negative的详细信息
    save_error_cases: true
    # 错误案例保存路径（相对于output_dir）
    error_cases_dir: "error_analysis"

# 评估指标权重配置（用于模型选择）
drc_metrics_weights:
  # 各指标在模型选择中的权重
  accuracy: 0.3      # Recall权重
  precision: 0.3     # Precision权重  
  f1_score: 0.4      # F1 Score权重（主要指标）
  
# 早停配置（基于DRC指标）
early_stopping:
  # 是否基于DRC指标进行早停
  enabled: false
  # 监控的指标名称
  monitor: "drc_f1_score"
  # 早停的耐心值（epoch数）
  patience: 10
  # 最小改善阈值
  min_delta: 0.001
