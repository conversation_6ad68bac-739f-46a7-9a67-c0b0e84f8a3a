
__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../drc_evaluation.yml',
  '../drc_loss_config.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r50vd.yml',
]


output_dir: ./output/new-data

# 🔥 DRC热点检测评估配置
drc_evaluation:
  # IoU阈值，用于判断预测框与GT框是否匹配
  iou_threshold: 0.6
  # 热点类别ID（1为热点，0为背景）
  hotspot_class_id: 1
  # 置信度阈值，只考虑置信度超过此值的预测框
  confidence_threshold: 0.6
  # 是否保存详细评估结果到JSON文件
  save_results: true
  # 是否在训练过程中启用DRC评估
  enabled: true

PResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True

HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.5


RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 3
  num_denoising: 100



optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?=.*norm).*$'
      lr: 0.00001
      weight_decay: 0.
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bias)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

