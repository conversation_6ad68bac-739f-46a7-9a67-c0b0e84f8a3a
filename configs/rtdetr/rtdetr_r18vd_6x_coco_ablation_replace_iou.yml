__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../drc_evaluation.yml',
  '../drc_loss_config.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r50vd.yml',
]

# 🔥 消融实验：替换L1+GIoU为简单IoU损失
# 实验目的：评估L1+GIoU损失组合对DRC热点检测性能的影响
output_dir: ./output/ablation/replace-IoU

# 🔥 DRC热点检测评估配置
drc_evaluation:
  # IoU阈值，用于判断预测框与GT框是否匹配
  iou_threshold: 0.5
  # 热点类别ID（1为热点，0为背景）
  hotspot_class_id: 1
  # 置信度阈值，只考虑置信度超过此值的预测框
  confidence_threshold: 0.6
  # 是否保存详细评估结果到JSON文件
  save_results: true
  # 是否在训练过程中启用DRC评估
  enabled: true

PResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True

HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.5

RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 3
  num_denoising: 100

# 🔥 消融实验配置：替换L1+GIoU为简单IoU损失
# 将'boxes'损失（包含L1和GIoU）替换为简单的'iou'损失
SetCriterion:
  weight_dict: {loss_vfl: 1, loss_iou: 7, loss_fppl: 1.5}
  losses: ['vfl', 'iou', 'fppl']
  alpha: 0.75
  gamma: 2.0

  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 🔥 消融实验训练配置
epoches: 20
clip_max_norm: 0.1

# 实验说明
experiment_info:
  name: "Replace L1+GIoU with Simple IoU Loss"
  description: "评估将L1+GIoU损失组合替换为简单IoU损失对DRC热点检测性能的影响"
  expected_impact: "定位精度可能下降，几何一致性变差，但计算更简单"
  baseline_losses: ['vfl', 'boxes', 'fppl']  # boxes包含L1+GIoU
  ablation_losses: ['vfl', 'iou', 'fppl']    # 替换为简单IoU
  replaced_components: ['L1 loss', 'GIoU loss']
  new_components: ['Simple IoU loss']
  training_epochs: 20
  loss_weight_explanation: |
    - loss_vfl: 1.0 (Varifocal Loss权重保持不变)
    - loss_iou: 7.0 (替代原来的loss_bbox(5.0) + loss_giou(2.0)的总权重)
    - loss_fppl: 1.5 (False Positive Penalty Loss权重保持不变)
  technical_details: |
    简单IoU损失实现：
    - loss_iou = 1 - IoU(pred_boxes, target_boxes)
    - 直接使用IoU值计算损失，不考虑L1距离和广义IoU
    - 优点：计算简单，直接优化IoU指标
    - 缺点：可能缺乏对边界框精确位置的细粒度约束
