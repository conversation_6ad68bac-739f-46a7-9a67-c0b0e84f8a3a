__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  '../drc_evaluation.yml',
  '../drc_loss_config.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r50vd.yml',
]

# 🔥 消融实验：将Varifocal Loss替换为标准Focal Loss
# 实验目的：评估VFL的质量感知机制对DRC热点检测性能的具体贡献
output_dir: ./output/ablation/replace-focal

# 🔥 DRC热点检测评估配置
drc_evaluation:
  # IoU阈值，用于判断预测框与GT框是否匹配
  iou_threshold: 0.6
  # 热点类别ID（1为热点，0为背景）
  hotspot_class_id: 1
  # 置信度阈值，只考虑置信度超过此值的预测框
  confidence_threshold: 0.6
  # 是否保存详细评估结果到JSON文件
  save_results: true
  # 是否在训练过程中启用DRC评估
  enabled: true

PResNet:
  depth: 18
  freeze_at: -1
  freeze_norm: False
  pretrained: True

HybridEncoder:
  in_channels: [128, 256, 512]
  hidden_dim: 256
  expansion: 0.5

RTDETRTransformer:
  eval_idx: -1
  num_decoder_layers: 3
  num_denoising: 100

# 🔥 消融实验配置：将Varifocal Loss替换为标准Focal Loss
# VFL使用IoU作为目标分数，Focal Loss使用二进制标签
SetCriterion:
  weight_dict: {loss_focal: 1, loss_bbox: 5, loss_giou: 2, loss_fppl: 1.5}
  losses: ['focal', 'boxes', 'fppl']
  alpha: 0.75
  gamma: 2.0

  matcher:
    type: HungarianMatcher
    weight_dict: {cost_class: 2, cost_bbox: 5, cost_giou: 2}
    alpha: 0.25
    gamma: 2.0

# 🔥 消融实验训练配置
epoches: 20
clip_max_norm: 0.1

# 实验说明
experiment_info:
  name: "Replace Varifocal Loss with Standard Focal Loss"
  description: "评估将Varifocal Loss替换为标准Focal Loss对DRC热点检测性能的影响"
  expected_impact: "质量感知能力下降，可能影响高质量检测框的识别精度"
  baseline_losses: ['vfl', 'boxes', 'fppl']
  ablation_losses: ['focal', 'boxes', 'fppl']
  replaced_components: ['Varifocal Loss (VFL)']
  new_components: ['Standard Focal Loss']
  training_epochs: 20
  
  # 技术对比分析
  technical_comparison:
    varifocal_loss:
      target_type: "IoU-weighted scores (continuous)"
      formula: "BCE with IoU-based target scores"
      quality_awareness: "Yes - uses IoU as quality measure"
      advantages: 
        - "质量感知的分类损失"
        - "高IoU目标获得更高的目标分数"
        - "更好的检测质量和定位精度的一致性"
      implementation: "target_score = IoU * one_hot_label"
      
    focal_loss:
      target_type: "Binary labels (0 or 1)"
      formula: "Standard sigmoid focal loss"
      quality_awareness: "No - treats all positive samples equally"
      advantages:
        - "难样本聚焦机制"
        - "缓解类别不平衡问题"
        - "计算简单，训练稳定"
      implementation: "target = one_hot(class_labels)"
      
  key_differences:
    - "VFL考虑检测框质量，Focal Loss只考虑类别"
    - "VFL使用连续目标分数，Focal Loss使用二进制标签"
    - "VFL能更好地区分高质量和低质量的正样本"
    - "Focal Loss对所有正样本一视同仁"
    
  expected_performance_changes:
    precision: "可能下降 - 缺少质量感知机制"
    recall: "可能保持 - 仍有难样本聚焦"
    ap_metrics: "可能下降 - 特别是高IoU阈值下的AP"
    localization_quality: "可能下降 - 缺少质量-分类一致性"
    training_stability: "可能更稳定 - 标准Focal Loss更成熟"
