
use_ema: True 
ema:
  type: ModelEMA
  decay: 0.9999
  warmups: 2000


find_unused_parameters: False

epoches: 100
clip_max_norm: 0.1

optimizer:
  type: AdamW
  params: 
    - 
      params: 'backbone'
      lr: 0.00001
    - 
      params: '^(?=.*encoder(?=.*bias|.*norm.*weight)).*$'
      weight_decay: 0.
    -
      params: '^(?=.*decoder(?=.*bias|.*norm.*weight)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001


lr_scheduler:
  type: MultiStepLR
  milestones: [1000]
  gamma: 0.1

